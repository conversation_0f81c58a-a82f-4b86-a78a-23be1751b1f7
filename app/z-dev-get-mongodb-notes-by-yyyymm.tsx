import React, { useState, useEffect } from 'react';

import {
    View,
    Text,
    StyleSheet,
    Image,
    TouchableOpacity,
    ImageBackground,
    Animated,
    StatusBar,
    Platform,
    ScrollView,
    ActivityIndicator,
    Alert
} from 'react-native';

import env from '../env';

import { router } from 'expo-router';

import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { Storage } from 'expo-sqlite/kv-store';

import { useSQLiteContext } from 'expo-sqlite';

import { HugeiconsIcon } from '@hugeicons/react-native';
import { ArrowLeft01Icon } from '@hugeicons/core-free-icons';



export default function ZDevGetMongodbNotesByYyyyMm() {
    const insets = useSafeAreaInsets();
    const db = useSQLiteContext();

    // 获取当前年月的函数
    const getCurrentYyyyMm = (): number => {
        const now = new Date();
        const year = now.getFullYear();
        const month = now.getMonth() + 1;
        return year * 100 + month; // 返回数字格式，如 202412
    };

    // 获取最近几个月的选项
    const getRecentMonths = (): Array<{ value: number, label: string }> => {
        const months = [];
        for (let i = 0; i < 6; i++) { // 获取最近6个月
            const date = new Date();
            date.setMonth(date.getMonth() - i);
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const value = year * 100 + month; // 数字格式，如 202412
            const monthStr = String(month).padStart(2, '0');
            const label = `${year}-${monthStr}`;
            months.push({ value, label });
        }
        return months;
    };

    // 状态管理
    const [loading, setLoading] = useState<boolean>(false);
    const [notes, setNotes] = useState<any[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [yyyymm, setYyyyMm] = useState<number>(getCurrentYyyyMm()); // 动态获取当前月份
    const [dbSaving, setDbSaving] = useState<boolean>(false); // 数据库写入状态

    // console.log('z-demo-page BASE_URL:', env.BASE_URL);

    const handleTopLeftBack = () => {
        router.back();
    };

    // 将 MongoDB 数据写入本地数据库的函数
    const saveNotesToLocalDb = async (mongoNotes: any[]) => {
        if (!mongoNotes || mongoNotes.length === 0) {
            console.log('没有要保存的数据');
            return { success: true, inserted: 0, updated: 0, skipped: 0 };
        }

        let inserted = 0;
        let updated = 0;
        let skipped = 0;

        try {
            for (const note of mongoNotes) {
                // 检查是否已存在相同的 mongodb_id 记录
                const existingNote = await db.getFirstAsync(
                    'SELECT id, sync_mongodb_time, sync_mongodb_device FROM notes WHERE mongodb_id = ?',
                    [note._id]
                );

                if (existingNote) {
                    // 如果本地记录有同步标记，说明是从本地同步到云端的，跳过更新
                    if ((existingNote as any).sync_mongodb_time || (existingNote as any).sync_mongodb_device) {
                        skipped++;
                        console.log(`跳过本地同步的笔记: ${note.title}`);
                        continue;
                    }
                    // 更新现有记录
                    await db.runAsync(`
                        UPDATE notes SET
                            title = ?,
                            content = ?,
                            mood_name = ?,
                            mood_score = ?,
                            mood_app_in_img_name = ?,
                            mood_user_create_url = ?,
                            yyyymm = ?,
                            sync_mongodb_time = ?,
                            sync_mongodb_device = ?,
                            soft_deleted_at = ?,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE mongodb_id = ?
                    `, [
                        note.title || '',
                        note.content || '',
                        note.mood_name || 'happy',
                        note.mood_score || 10,
                        note.mood_app_in_img_name || 'mood1',
                        note.mood_user_create_url || null,
                        note.yyyymm || 199911,
                        note.sync_mongodb_time || note.z_created_at || null,
                        note.sync_mongodb_device || null,
                        note.soft_deleted_at || null,
                        note._id
                    ]);
                    updated++;
                    console.log(`更新了笔记: ${note.title}`);
                } else {
                    // 插入新记录
                    await db.runAsync(`
                        INSERT INTO notes (
                            mongodb_id,
                            title,
                            content,
                            mood_name,
                            mood_score,
                            mood_app_in_img_name,
                            mood_user_create_url,
                            yyyymm,
                            sync_mongodb_time,
                            sync_mongodb_device,
                            soft_deleted_at,
                            created_at,
                            updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    `, [
                        note._id,
                        note.title || '',
                        note.content || '',
                        note.mood_name || 'happy',
                        note.mood_score || 10,
                        note.mood_app_in_img_name || 'mood1',
                        note.mood_user_create_url || null,
                        note.yyyymm || 199911,
                        note.sync_mongodb_time || note.z_created_at || null,
                        note.sync_mongodb_device || null,
                        note.soft_deleted_at || null,
                        note.created_at || note.z_created_at || new Date().toISOString()
                    ]);
                    inserted++;
                    console.log(`插入了新笔记: ${note.title}`);
                }
            }

            console.log(`数据库写入完成: 插入 ${inserted} 条, 更新 ${updated} 条, 跳过 ${skipped} 条`);
            return { success: true, inserted, updated, skipped };

        } catch (error) {
            console.error('写入本地数据库时出错:', error);
            throw error;
        }
    };

    // API 调用函数
    const fetchNotesByYyyyMm = async (yearMonth: number) => {
        setLoading(true);
        setError(null);

        try {
            console.log('开始调用 API，查询月份:', yearMonth);

            // 获取用户 token
            const token = await Storage.getItem('userToken');
            console.log('本地获取到的 token:', token ? '已获取' : '未获取到');

            let api_url = env.BASE_URL + '/x_mooood_note';
            console.log('api_url:', api_url);

            const response = await fetch(api_url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                },
                body: JSON.stringify({
                    do_sth: 'get_my_notes_by_yyyymm',
                    yyyymm: yearMonth,
                }),
            });

            console.log('API 响应状态:', response.status);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('API 响应数据:', data);
            console.log('API 响应数据 JSON:', JSON.stringify(data));

            // 获取API返回的笔记数据
            const apiNotes = data.data || data.notes || [];
            setNotes(apiNotes);

            // 尝试将数据写入本地数据库
            setDbSaving(true);
            try {
                const saveResult = await saveNotesToLocalDb(apiNotes);

                Alert.alert(
                    '成功',
                    `API调用成功！
                    
📊 API数据: ${data.list_length || apiNotes.length} 条笔记
💾 本地数据库:
  • 新增: ${saveResult.inserted} 条
  • 更新: ${saveResult.updated} 条
  • 跳过: ${saveResult.skipped} 条

数据已成功同步到本地数据库！`,
                    [{ text: '确定' }]
                );

            } catch (dbError) {
                console.error('写入本地数据库失败:', dbError);
                Alert.alert(
                    '部分成功',
                    `API调用成功，获取了 ${data.list_length || apiNotes.length} 条笔记，但写入本地数据库时出错。

错误信息: ${dbError instanceof Error ? dbError.message : '未知错误'}`,
                    [{ text: '确定' }]
                );
            } finally {
                setDbSaving(false);
            }

        } catch (err) {
            console.error('API 调用错误:', err);
            const errorMessage = err instanceof Error ? err.message : '未知错误';
            setError(errorMessage);

            Alert.alert(
                '错误',
                `API 调用失败: ${errorMessage}`,
                [{ text: '确定' }]
            );
        } finally {
            setLoading(false);
        }
    };

    // 手动将当前显示的数据写入本地数据库
    const handleManualSaveToDb = async () => {
        if (notes.length === 0) {
            Alert.alert('提示', '没有可保存的数据，请先调用API获取数据');
            return;
        }

        setDbSaving(true);
        try {
            const saveResult = await saveNotesToLocalDb(notes);

            Alert.alert(
                '保存成功',
                `数据已成功写入本地数据库！

💾 保存结果:
  • 新增: ${saveResult.inserted} 条
  • 更新: ${saveResult.updated} 条  
  • 跳过: ${saveResult.skipped} 条`,
                [{ text: '确定' }]
            );

        } catch (error) {
            console.error('手动保存到数据库失败:', error);
            Alert.alert(
                '保存失败',
                `写入本地数据库时出错。

错误信息: ${error instanceof Error ? error.message : '未知错误'}`,
                [{ text: '确定' }]
            );
        } finally {
            setDbSaving(false);
        }
    };

    return (
        <>
            <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
            <ImageBackground
                source={require('../assets/images/bg4.png')}
                style={styles.backgroundImage}
            >
                <View style={[styles.all, { paddingTop: insets.top }]}>
                    <View style={styles.head_nav}>
                        <TouchableOpacity onPress={handleTopLeftBack}>
                            <HugeiconsIcon
                                icon={ArrowLeft01Icon}
                                size={40}
                                color="black"
                                strokeWidth={1.2}
                            />
                        </TouchableOpacity>

                        <Text style={styles.head_nav_title}> get notes by yyyymm </Text>

                        <HugeiconsIcon
                            icon={ArrowLeft01Icon}
                            size={40}
                            color="#00000000" // 设置为透明
                            strokeWidth={1.2}
                        />
                    </View>

                    <View style={styles.content}>
                        <View style={styles.apiSection}>
                            <Text style={styles.sectionTitle}>MongoDB 笔记查询</Text>

                            <View style={styles.inputSection}>
                                <Text style={styles.label}>查询月份: {yyyymm}</Text>
                                <ScrollView
                                    horizontal
                                    showsHorizontalScrollIndicator={false}
                                    style={styles.monthScrollView}
                                >
                                    <View style={styles.buttonRow}>
                                        {getRecentMonths().map((month) => (
                                            <TouchableOpacity
                                                key={month.value}
                                                style={[
                                                    styles.monthButton,
                                                    yyyymm === month.value && styles.selectedMonthButton
                                                ]}
                                                onPress={() => setYyyyMm(month.value)}
                                            >
                                                <Text style={[
                                                    styles.buttonText,
                                                    yyyymm === month.value && styles.selectedButtonText
                                                ]}>
                                                    {month.label}
                                                </Text>
                                            </TouchableOpacity>
                                        ))}
                                    </View>
                                </ScrollView>
                            </View>

                            <TouchableOpacity
                                style={[styles.apiButton, loading && styles.disabledButton]}
                                onPress={() => fetchNotesByYyyyMm(yyyymm)}
                                disabled={loading}
                            >
                                {loading ? (
                                    <ActivityIndicator color="white" />
                                ) : (
                                    <Text style={styles.apiButtonText}>
                                        调用 API 获取笔记
                                    </Text>
                                )}
                            </TouchableOpacity>

                            {notes.length > 0 && (
                                <TouchableOpacity
                                    style={[styles.dbButton, dbSaving && styles.disabledButton]}
                                    onPress={handleManualSaveToDb}
                                    disabled={dbSaving}
                                >
                                    {dbSaving ? (
                                        <ActivityIndicator color="white" />
                                    ) : (
                                        <Text style={styles.dbButtonText}>
                                            💾 手动保存到本地数据库
                                        </Text>
                                    )}
                                </TouchableOpacity>
                            )}

                            {error && (
                                <View style={styles.errorContainer}>
                                    <Text style={styles.errorText}>错误: {error}</Text>
                                </View>
                            )}

                            <ScrollView style={styles.resultsContainer}>
                                <Text style={styles.resultsTitle}>
                                    查询结果 ({notes.length} 条)
                                </Text>

                                {notes.length > 0 ? (
                                    notes.map((note, index) => (
                                        <View key={index} style={styles.noteItem}>
                                            <Text style={styles.noteTitle}>
                                                {note.title || `笔记 ${index + 1}`}
                                            </Text>
                                            <Text style={styles.noteContent}>
                                                {note.content || '无内容'}
                                            </Text>
                                            <Text style={styles.noteDate}>
                                                {note.date || note.created_at || '无日期'}
                                            </Text>
                                        </View>
                                    ))
                                ) : (
                                    <Text style={styles.noDataText}>
                                        {loading ? '加载中...' : '暂无数据，点击按钮调用 API'}
                                    </Text>
                                )}
                            </ScrollView>
                        </View>
                    </View>
                </View>
            </ImageBackground>
        </>

    );
}

const styles = StyleSheet.create({
    backgroundImage: {
        flex: 1,

        width: '100%',
        height: '100%',
    },

    all: {
        flex: 1,

        // justifyContent: 'center',
        // alignItems: 'center',
        // backgroundColor: '#f40',
    },

    head_nav: {
        height: 50,
        width: '100%',

        // paddingHorizontal: 5,

        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',

        // backgroundColor: '#f40',
    },
    head_nav_title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },


    content: {
        flex: 1,
        padding: 16,
        // backgroundColor: 'white',
    },

    apiSection: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderRadius: 12,
        padding: 16,
        marginBottom: 16,
    },

    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 16,
        textAlign: 'center',
    },

    inputSection: {
        marginBottom: 16,
    },

    label: {
        fontSize: 16,
        color: '#333',
        marginBottom: 8,
        fontWeight: '500',
    },

    monthScrollView: {
        marginBottom: 8,
    },

    buttonRow: {
        flexDirection: 'row',
        paddingHorizontal: 8,
    },

    monthButton: {
        backgroundColor: '#007AFF',
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 8,
        minWidth: 80,
        alignItems: 'center',
        marginHorizontal: 4,
    },

    selectedMonthButton: {
        backgroundColor: '#34C759',
    },

    buttonText: {
        color: 'white',
        fontSize: 14,
        fontWeight: '500',
    },

    selectedButtonText: {
        fontWeight: 'bold',
    },

    apiButton: {
        backgroundColor: '#34C759',
        paddingVertical: 12,
        paddingHorizontal: 24,
        borderRadius: 8,
        alignItems: 'center',
        marginBottom: 16,
    },

    disabledButton: {
        backgroundColor: '#999',
    },

    apiButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: 'bold',
    },

    dbButton: {
        backgroundColor: '#FF9500',
        paddingVertical: 12,
        paddingHorizontal: 24,
        borderRadius: 8,
        alignItems: 'center',
        marginBottom: 16,
    },

    dbButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: 'bold',
    },

    errorContainer: {
        backgroundColor: '#FFE6E6',
        padding: 12,
        borderRadius: 8,
        marginBottom: 16,
        borderLeftWidth: 4,
        borderLeftColor: '#FF3B30',
    },

    errorText: {
        color: '#FF3B30',
        fontSize: 14,
    },

    resultsContainer: {
        maxHeight: 300,
        backgroundColor: 'rgba(240, 240, 240, 0.8)',
        borderRadius: 8,
        padding: 12,
    },

    resultsTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 12,
    },

    noteItem: {
        backgroundColor: 'white',
        padding: 12,
        marginBottom: 8,
        borderRadius: 8,
        borderLeftWidth: 3,
        borderLeftColor: '#007AFF',
    },

    noteTitle: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 4,
    },

    noteContent: {
        fontSize: 12,
        color: '#666',
        marginBottom: 4,
        lineHeight: 16,
    },

    noteDate: {
        fontSize: 10,
        color: '#999',
        fontStyle: 'italic',
    },

    noDataText: {
        textAlign: 'center',
        color: '#666',
        fontSize: 14,
        fontStyle: 'italic',
        marginTop: 20,
    },
});
